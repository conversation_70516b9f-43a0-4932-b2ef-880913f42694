{"format": 1, "restore": {"D:\\Unity_Project\\SurAnim\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"D:\\Unity_Project\\SurAnim\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj"}, "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj", "projectName": "Unity.2D.Sprite.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.2D.Sprite.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj", "projectName": "Unity.EditorCoroutines.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.EditorCoroutines.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj", "projectName": "Unity.InputSystem", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.InputSystem\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj", "projectName": "Unity.InputSystem.ForUI", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.InputSystem.ForUI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj", "projectName": "Unity.Mathematics", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Mathematics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj", "projectName": "Unity.Mathematics.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Mathematics.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj", "projectName": "Unity.Performance.Profile-Analyzer.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Performance.Profile-Analyzer.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj", "projectName": "Unity.PlasticSCM.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.PlasticSCM.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj", "projectName": "Unity.Rider.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Rider.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj", "projectName": "Unity.Settings.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Settings.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.TestTools.CodeCoverage.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj", "projectName": "Unity.TextMeshPro", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.TextMeshPro\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj", "projectName": "Unity.TextMeshPro.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.TextMeshPro.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj", "projectName": "Unity.Timeline", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Timeline\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj", "projectName": "Unity.Timeline.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.Timeline.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj", "projectName": "Unity.VisualScripting.Core", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.Core\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj", "projectName": "Unity.VisualScripting.Core.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj", "projectName": "Unity.VisualScripting.Flow", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.Flow\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj", "projectName": "Unity.VisualScripting.Flow.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.Flow.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj", "projectName": "Unity.VisualScripting.SettingsProvider.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.SettingsProvider.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj", "projectName": "Unity.VisualScripting.Shared.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.Shared.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj", "projectName": "Unity.VisualScripting.State", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.State\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj", "projectName": "Unity.VisualScripting.State.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualScripting.State.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj", "projectName": "Unity.VisualStudio.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VisualStudio.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj", "projectName": "Unity.VSCode.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.VSCode.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj", "projectName": "Unity.XR.ARAnalytics.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARAnalytics.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj", "projectName": "Unity.XR.ARCore", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARCore\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj", "projectName": "Unity.XR.ARCore.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARCore.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj", "projectName": "Unity.XR.ARFoundation", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARFoundation\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj", "projectName": "Unity.XR.ARFoundation.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARFoundation.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj", "projectName": "Unity.XR.ARFoundation.InternalUtils", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARFoundation.InternalUtils\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj", "projectName": "Unity.XR.ARFoundation.VisualScripting", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARFoundation.VisualScripting\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj", "projectName": "Unity.XR.ARFoundation.VisualScripting.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARFoundation.VisualScripting.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj", "projectName": "Unity.XR.ARKit", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARKit\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj", "projectName": "Unity.XR.ARKit.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARKit.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj", "projectName": "Unity.XR.ARKit.FaceTracking", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARKit.FaceTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj", "projectName": "Unity.XR.ARSubsystems", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARSubsystems\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj", "projectName": "Unity.XR.ARSubsystems.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARSubsystems.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj", "projectName": "Unity.XR.CoreUtils", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.CoreUtils\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj", "projectName": "Unity.XR.CoreUtils.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.CoreUtils.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj", "projectName": "Unity.XR.Management", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.Management\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj", "projectName": "Unity.XR.Management.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.Management.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj", "projectName": "Unity.XR.Simulation", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.Simulation\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj", "projectName": "Unity.XR.Simulation.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.Simulation.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj", "projectName": "UnityEditor.SpatialTracking", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEditor.SpatialTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj", "projectName": "UnityEditor.XR.LegacyInputHelpers", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEditor.XR.LegacyInputHelpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj", "projectName": "UnityEngine.SpatialTracking", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEngine.SpatialTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj", "projectName": "UnityEngine.XR.LegacyInputHelpers", "projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\UnityEngine.XR.LegacyInputHelpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}}}