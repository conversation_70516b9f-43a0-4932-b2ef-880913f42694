using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.SceneManagement;

public class QRCodeContoller : MonoBehaviour
{
    
    public string URL = "https://surgassistsbackendar.online";

    public QRCodeDecodeController qrCodeDecodeController;

    public TextMeshProUGUI textMesh;

    public void ResetScanner()
    {
        qrCodeDecodeController.Reset();
    }

    public void SetString(string KeyName, string Value)
    {
        PlayerPrefs.SetString(KeyName, Value);
    }


    public void QrCallback(string result)
    {
        Debug.Log(result);
        textMesh.text = result;

        string urlSend = URL + "/unity-ar-status/" + result;

        Debug.Log(urlSend);


        SetString("QRCode", result);


        StartCoroutine(GetRequest(urlSend));
        
    }


 



    IEnumerator GetRequest(string uri)
    {
        UnityWebRequest uwr = UnityWebRequest.Get(uri);

        uwr.SetRequestHeader("Authorization", "Bearer " + "NzssOajXMGoFK1gQOjS0MNDejDp0V9v4Jhj7JqQQD1A1A5XTtpw6hfd2TIzxruqE");


        yield return uwr.SendWebRequest();

        if (uwr.result != UnityWebRequest.Result.Success)
        {
            Debug.Log("Error While Sending: " + uwr.error);
        }
        else
        {
            Debug.Log("Received: " + uwr.downloadHandler.text);

            SceneManager.LoadScene("MainForTimeline", LoadSceneMode.Single);
        }
    }

}
