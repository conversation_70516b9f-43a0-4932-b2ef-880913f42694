Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.43f1 (85497d293fa1) revision 8735101'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'en' Physical Memory: 65416 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity_Project/SurAnim
-logFile
Logs/AssetImportWorker0.log
-srvPort
53322
Successfully changed project path to: D:/Unity_Project/SurAnim
D:/Unity_Project/SurAnim
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [376]  Target information:

Player connection [376]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4041045040 [EditorId] 4041045040 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-AE5T6LH) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [376]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4041045040 [EditorId] 4041045040 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-AE5T6LH) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [376] Host joined multi-casting on [***********:54997]...
Player connection [376] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 10.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.43f1 (85497d293fa1)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity_Project/SurAnim/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3070 (ID=0x2484)
    Vendor:   NVIDIA
    VRAM:     8018 MB
    Driver:   32.0.15.7260
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56848
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.016814 seconds.
- Loaded All Assemblies, in  0.365 seconds
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 229 ms
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.537 seconds
Domain Reload Profiling: 899ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (152ms)
		LoadAssemblies (101ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (149ms)
			TypeCache.Refresh (148ms)
				TypeCache.ScanAssembly (135ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (537ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (342ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (106ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.781 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 1661ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (527ms)
		LoadAssemblies (387ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (200ms)
				TypeCache.ScanAssembly (180ms)
			ScanForSourceGeneratedMonoScriptInfo (23ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (702ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (558ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 9.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4600 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (1.2 MB). Loaded Objects now: 5081.
Memory consumption went from 161.9 MB to 160.6 MB.
Total: 4.828400 ms (FindLiveObjects: 0.513000 ms CreateObjectMapping: 0.441900 ms MarkObjects: 3.187900 ms  DeleteObjects: 0.685000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 221121.155304 seconds.
  path: Assets/Scenes/MainForTimeline.unity
  artifactKey: Guid(76cc93b02937143499f6e0a0283e3986) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/MainForTimeline.unity using Guid(76cc93b02937143499f6e0a0283e3986) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'e70b519f5ac1bd2ae6cfff4fb92c3fe4') in 0.002307 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.494 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.089 seconds
Domain Reload Profiling: 1580ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (263ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1089ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (563ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (414ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 11.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4568 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.2 MB). Loaded Objects now: 5085.
Memory consumption went from 155.6 MB to 154.4 MB.
Total: 5.623800 ms (FindLiveObjects: 0.485400 ms CreateObjectMapping: 0.451800 ms MarkObjects: 4.137100 ms  DeleteObjects: 0.548700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 10.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7 unused Assets / (1.1 MB). Loaded Objects now: 5087.
Memory consumption went from 85.0 MB to 83.9 MB.
Total: 7.825000 ms (FindLiveObjects: 0.675800 ms CreateObjectMapping: 0.586000 ms MarkObjects: 5.719300 ms  DeleteObjects: 0.841400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 7212.321355 seconds.
  path: Assets/CODE/Reports.cs
  artifactKey: Guid(2893b793bece2d8489f0d9afe9c66e5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CODE/Reports.cs using Guid(2893b793bece2d8489f0d9afe9c66e5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '5c33a358cb2f43e1b5fa44b6cebae02a') in 0.004829 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.481 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.112 seconds
Domain Reload Profiling: 1589ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (263ms)
		LoadAssemblies (305ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (37ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1112ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (418ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 12.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4567 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.1 MB). Loaded Objects now: 5092.
Memory consumption went from 155.1 MB to 154.0 MB.
Total: 6.356800 ms (FindLiveObjects: 0.460300 ms CreateObjectMapping: 0.431400 ms MarkObjects: 4.896500 ms  DeleteObjects: 0.567700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 298ffd25b301d719d3458f47d11fdada -> e02b95c3f5f63f472a91d7ac99e710c1
========================================================================
Received Import Request.
  Time since last request: 7.080395 seconds.
  path: Assets/CODE/Reports.cs
  artifactKey: Guid(2893b793bece2d8489f0d9afe9c66e5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CODE/Reports.cs using Guid(2893b793bece2d8489f0d9afe9c66e5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '1f9a4aabec89f22b6231651d39f69a13') in 0.003079 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.480 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.107 seconds
Domain Reload Profiling: 1584ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (263ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1108ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (432ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 8.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4567 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.1 MB). Loaded Objects now: 5096.
Memory consumption went from 155.2 MB to 154.1 MB.
Total: 3.994600 ms (FindLiveObjects: 0.338800 ms CreateObjectMapping: 0.354400 ms MarkObjects: 2.767700 ms  DeleteObjects: 0.532900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 298ffd25b301d719d3458f47d11fdada -> e02b95c3f5f63f472a91d7ac99e710c1
========================================================================
Received Import Request.
  Time since last request: 667.786262 seconds.
  path: Assets/Scenes/QR.unity
  artifactKey: Guid(b64b8bac48952974fba4c6cdbc9e185c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/QR.unity using Guid(b64b8bac48952974fba4c6cdbc9e185c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'd1adcd7e35dffc81b397f3964257918e') in 0.002688 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.471 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.064 seconds
Domain Reload Profiling: 1531ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (302ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1064ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (549ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (408ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 9.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4567 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.2 MB). Loaded Objects now: 5100.
Memory consumption went from 155.2 MB to 154.1 MB.
Total: 4.387200 ms (FindLiveObjects: 0.450100 ms CreateObjectMapping: 0.432500 ms MarkObjects: 2.950900 ms  DeleteObjects: 0.553000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 782f49c966dcc532b057e35b15704485 -> e02b95c3f5f63f472a91d7ac99e710c1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.471 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.068 seconds
Domain Reload Profiling: 1536ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (302ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1069ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (552ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (408ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 9.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4568 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.2 MB). Loaded Objects now: 5104.
Memory consumption went from 155.5 MB to 154.3 MB.
Total: 3.742400 ms (FindLiveObjects: 0.311300 ms CreateObjectMapping: 0.266000 ms MarkObjects: 2.648500 ms  DeleteObjects: 0.515900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 782f49c966dcc532b057e35b15704485 -> e02b95c3f5f63f472a91d7ac99e710c1
