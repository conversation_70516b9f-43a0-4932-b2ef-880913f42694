{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.XR.ARFoundation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.dll": {}}}, "Unity.XR.ARFoundation.InternalUtils/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.InternalUtils.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.InternalUtils.dll": {}}}, "Unity.XR.ARKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARKit.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARKit.dll": {}}}, "Unity.XR.ARSubsystems/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARSubsystems.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARSubsystems.dll": {}}}, "Unity.XR.ARSubsystems.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARSubsystems.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARSubsystems.Editor.dll": {}}}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}}, "Unity.XR.Management/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.dll": {}}}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.Management": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "path": "Unity.EditorCoroutines.Editor.csproj", "msbuildProject": "Unity.EditorCoroutines.Editor.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.XR.ARFoundation/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.csproj", "msbuildProject": "Unity.XR.ARFoundation.csproj"}, "Unity.XR.ARFoundation.InternalUtils/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.InternalUtils.csproj", "msbuildProject": "Unity.XR.ARFoundation.InternalUtils.csproj"}, "Unity.XR.ARKit/1.0.0": {"type": "project", "path": "Unity.XR.ARKit.csproj", "msbuildProject": "Unity.XR.ARKit.csproj"}, "Unity.XR.ARSubsystems/1.0.0": {"type": "project", "path": "Unity.XR.ARSubsystems.csproj", "msbuildProject": "Unity.XR.ARSubsystems.csproj"}, "Unity.XR.ARSubsystems.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARSubsystems.Editor.csproj", "msbuildProject": "Unity.XR.ARSubsystems.Editor.csproj"}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.csproj", "msbuildProject": "Unity.XR.CoreUtils.csproj"}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.Editor.csproj", "msbuildProject": "Unity.XR.CoreUtils.Editor.csproj"}, "Unity.XR.Management/1.0.0": {"type": "project", "path": "Unity.XR.Management.csproj", "msbuildProject": "Unity.XR.Management.csproj"}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Management.Editor.csproj", "msbuildProject": "Unity.XR.Management.Editor.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEngine.SpatialTracking.csproj", "msbuildProject": "UnityEngine.SpatialTracking.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.EditorCoroutines.Editor >= 1.0.0", "Unity.XR.ARFoundation >= 1.0.0", "Unity.XR.ARKit >= 1.0.0", "Unity.XR.ARSubsystems >= 1.0.0", "Unity.XR.ARSubsystems.Editor >= 1.0.0", "Unity.XR.CoreUtils.Editor >= 1.0.0", "Unity.XR.Management >= 1.0.0", "Unity.XR.Management.Editor >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj", "projectName": "Unity.XR.ARKit.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.XR.ARKit.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}}