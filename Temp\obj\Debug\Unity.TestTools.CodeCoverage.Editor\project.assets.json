{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.Settings.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Settings.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Settings.Editor.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Unity.Settings.Editor/1.0.0": {"type": "project", "path": "Unity.Settings.Editor.csproj", "msbuildProject": "Unity.Settings.Editor.csproj"}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.Settings.Editor >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Unity.TestTools.CodeCoverage.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Settings.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}}