{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Assembly-CSharp-firstpass": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.EditorCoroutines.Editor": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.InputSystem.ForUI": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.Performance.Profile-Analyzer.Editor": "1.0.0", "Unity.PlasticSCM.Editor": "1.0.0", "Unity.Rider.Editor": "1.0.0", "Unity.TestTools.CodeCoverage.Editor": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "Unity.VSCode.Editor": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.SettingsProvider.Editor": "1.0.0", "Unity.VisualScripting.Shared.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "Unity.VisualStudio.Editor": "1.0.0", "Unity.XR.ARAnalytics.Editor": "1.0.0", "Unity.XR.ARCore": "1.0.0", "Unity.XR.ARCore.Editor": "1.0.0", "Unity.XR.ARFoundation": "1.0.0", "Unity.XR.ARFoundation.Editor": "1.0.0", "Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.ARFoundation.VisualScripting": "1.0.0", "Unity.XR.ARFoundation.VisualScripting.Editor": "1.0.0", "Unity.XR.ARKit": "1.0.0", "Unity.XR.ARKit.Editor": "1.0.0", "Unity.XR.ARKit.FaceTracking": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.ARSubsystems.Editor": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Simulation": "1.0.0", "Unity.XR.Simulation.Editor": "1.0.0", "UnityEditor.SpatialTracking": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEditor.XR.LegacyInputHelpers": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Sprite.Editor": "1.0.0", "Unity.EditorCoroutines.Editor": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.InputSystem.ForUI": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.Performance.Profile-Analyzer.Editor": "1.0.0", "Unity.PlasticSCM.Editor": "1.0.0", "Unity.Rider.Editor": "1.0.0", "Unity.TestTools.CodeCoverage.Editor": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "Unity.VSCode.Editor": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.SettingsProvider.Editor": "1.0.0", "Unity.VisualScripting.Shared.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "Unity.VisualStudio.Editor": "1.0.0", "Unity.XR.ARAnalytics.Editor": "1.0.0", "Unity.XR.ARCore": "1.0.0", "Unity.XR.ARCore.Editor": "1.0.0", "Unity.XR.ARFoundation": "1.0.0", "Unity.XR.ARFoundation.Editor": "1.0.0", "Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.ARFoundation.VisualScripting": "1.0.0", "Unity.XR.ARFoundation.VisualScripting.Editor": "1.0.0", "Unity.XR.ARKit": "1.0.0", "Unity.XR.ARKit.Editor": "1.0.0", "Unity.XR.ARKit.FaceTracking": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.ARSubsystems.Editor": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Simulation": "1.0.0", "Unity.XR.Simulation.Editor": "1.0.0", "UnityEditor.SpatialTracking": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEditor.XR.LegacyInputHelpers": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.Performance.Profile-Analyzer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Performance.Profile-Analyzer.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Performance.Profile-Analyzer.Editor.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.Settings.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Settings.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Settings.Editor.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Settings.Editor": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "Unity.VSCode.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VSCode.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VSCode.Editor.dll": {}}}, "Unity.XR.ARAnalytics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARAnalytics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARAnalytics.Editor.dll": {}}}, "Unity.XR.ARCore/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARCore.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARCore.dll": {}}}, "Unity.XR.ARCore.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARCore": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.ARSubsystems.Editor": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARCore.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARCore.Editor.dll": {}}}, "Unity.XR.ARFoundation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.dll": {}}}, "Unity.XR.ARFoundation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.ARAnalytics.Editor": "1.0.0", "Unity.XR.ARFoundation": "1.0.0", "Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.Editor.dll": {}}}, "Unity.XR.ARFoundation.InternalUtils/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.InternalUtils.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.InternalUtils.dll": {}}}, "Unity.XR.ARFoundation.VisualScripting/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.XR.ARFoundation": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.VisualScripting.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.VisualScripting.dll": {}}}, "Unity.XR.ARFoundation.VisualScripting.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.XR.ARFoundation": "1.0.0", "Unity.XR.ARFoundation.VisualScripting": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARFoundation.VisualScripting.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARFoundation.VisualScripting.Editor.dll": {}}}, "Unity.XR.ARKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARKit.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARKit.dll": {}}}, "Unity.XR.ARKit.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.EditorCoroutines.Editor": "1.0.0", "Unity.XR.ARFoundation": "1.0.0", "Unity.XR.ARKit": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.ARSubsystems.Editor": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARKit.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARKit.Editor.dll": {}}}, "Unity.XR.ARKit.FaceTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARSubsystems": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARKit.FaceTracking.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARKit.FaceTracking.dll": {}}}, "Unity.XR.ARSubsystems/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARSubsystems.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARSubsystems.dll": {}}}, "Unity.XR.ARSubsystems.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.ARSubsystems.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.ARSubsystems.Editor.dll": {}}}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}}, "Unity.XR.Management/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.dll": {}}}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.Management": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}}, "Unity.XR.Simulation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.ARSubsystems": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Simulation.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Simulation.dll": {}}}, "Unity.XR.Simulation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.EditorCoroutines.Editor": "1.0.0", "Unity.XR.ARAnalytics.Editor": "1.0.0", "Unity.XR.ARFoundation.InternalUtils": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Simulation": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Simulation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Simulation.Editor.dll": {}}}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}}}}, "libraries": {"Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Sprite.Editor.csproj", "msbuildProject": "Unity.2D.Sprite.Editor.csproj"}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "path": "Unity.EditorCoroutines.Editor.csproj", "msbuildProject": "Unity.EditorCoroutines.Editor.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "path": "Unity.InputSystem.ForUI.csproj", "msbuildProject": "Unity.InputSystem.ForUI.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.Performance.Profile-Analyzer.Editor/1.0.0": {"type": "project", "path": "Unity.Performance.Profile-Analyzer.Editor.csproj", "msbuildProject": "Unity.Performance.Profile-Analyzer.Editor.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.Settings.Editor/1.0.0": {"type": "project", "path": "Unity.Settings.Editor.csproj", "msbuildProject": "Unity.Settings.Editor.csproj"}, "Unity.TestTools.CodeCoverage.Editor/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.csproj"}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Core.Editor.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Flow.Editor.csproj"}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.SettingsProvider.Editor.csproj", "msbuildProject": "Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Shared.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Shared.Editor.csproj"}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.csproj", "msbuildProject": "Unity.VisualScripting.State.csproj"}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.Editor.csproj", "msbuildProject": "Unity.VisualScripting.State.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "Unity.VSCode.Editor/1.0.0": {"type": "project", "path": "Unity.VSCode.Editor.csproj", "msbuildProject": "Unity.VSCode.Editor.csproj"}, "Unity.XR.ARAnalytics.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARAnalytics.Editor.csproj", "msbuildProject": "Unity.XR.ARAnalytics.Editor.csproj"}, "Unity.XR.ARCore/1.0.0": {"type": "project", "path": "Unity.XR.ARCore.csproj", "msbuildProject": "Unity.XR.ARCore.csproj"}, "Unity.XR.ARCore.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARCore.Editor.csproj", "msbuildProject": "Unity.XR.ARCore.Editor.csproj"}, "Unity.XR.ARFoundation/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.csproj", "msbuildProject": "Unity.XR.ARFoundation.csproj"}, "Unity.XR.ARFoundation.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.Editor.csproj", "msbuildProject": "Unity.XR.ARFoundation.Editor.csproj"}, "Unity.XR.ARFoundation.InternalUtils/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.InternalUtils.csproj", "msbuildProject": "Unity.XR.ARFoundation.InternalUtils.csproj"}, "Unity.XR.ARFoundation.VisualScripting/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.VisualScripting.csproj", "msbuildProject": "Unity.XR.ARFoundation.VisualScripting.csproj"}, "Unity.XR.ARFoundation.VisualScripting.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARFoundation.VisualScripting.Editor.csproj", "msbuildProject": "Unity.XR.ARFoundation.VisualScripting.Editor.csproj"}, "Unity.XR.ARKit/1.0.0": {"type": "project", "path": "Unity.XR.ARKit.csproj", "msbuildProject": "Unity.XR.ARKit.csproj"}, "Unity.XR.ARKit.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARKit.Editor.csproj", "msbuildProject": "Unity.XR.ARKit.Editor.csproj"}, "Unity.XR.ARKit.FaceTracking/1.0.0": {"type": "project", "path": "Unity.XR.ARKit.FaceTracking.csproj", "msbuildProject": "Unity.XR.ARKit.FaceTracking.csproj"}, "Unity.XR.ARSubsystems/1.0.0": {"type": "project", "path": "Unity.XR.ARSubsystems.csproj", "msbuildProject": "Unity.XR.ARSubsystems.csproj"}, "Unity.XR.ARSubsystems.Editor/1.0.0": {"type": "project", "path": "Unity.XR.ARSubsystems.Editor.csproj", "msbuildProject": "Unity.XR.ARSubsystems.Editor.csproj"}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.csproj", "msbuildProject": "Unity.XR.CoreUtils.csproj"}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.Editor.csproj", "msbuildProject": "Unity.XR.CoreUtils.Editor.csproj"}, "Unity.XR.Management/1.0.0": {"type": "project", "path": "Unity.XR.Management.csproj", "msbuildProject": "Unity.XR.Management.csproj"}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Management.Editor.csproj", "msbuildProject": "Unity.XR.Management.Editor.csproj"}, "Unity.XR.Simulation/1.0.0": {"type": "project", "path": "Unity.XR.Simulation.csproj", "msbuildProject": "Unity.XR.Simulation.csproj"}, "Unity.XR.Simulation.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Simulation.Editor.csproj", "msbuildProject": "Unity.XR.Simulation.Editor.csproj"}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEditor.SpatialTracking.csproj", "msbuildProject": "UnityEditor.SpatialTracking.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEditor.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEditor.XR.LegacyInputHelpers.csproj"}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEngine.SpatialTracking.csproj", "msbuildProject": "UnityEngine.SpatialTracking.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEngine.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEngine.XR.LegacyInputHelpers.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp >= 1.0.0", "Assembly-CSharp-firstpass >= 1.0.0", "Unity.2D.Sprite.Editor >= 1.0.0", "Unity.EditorCoroutines.Editor >= 1.0.0", "Unity.InputSystem >= 1.0.0", "Unity.InputSystem.ForUI >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.Performance.Profile-Analyzer.Editor >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VSCode.Editor >= 1.0.0", "Unity.VisualScripting.Core >= 1.0.0", "Unity.VisualScripting.Core.Editor >= 1.0.0", "Unity.VisualScripting.Flow >= 1.0.0", "Unity.VisualScripting.Flow.Editor >= 1.0.0", "Unity.VisualScripting.SettingsProvider.Editor >= 1.0.0", "Unity.VisualScripting.Shared.Editor >= 1.0.0", "Unity.VisualScripting.State >= 1.0.0", "Unity.VisualScripting.State.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "Unity.XR.ARAnalytics.Editor >= 1.0.0", "Unity.XR.ARCore >= 1.0.0", "Unity.XR.ARCore.Editor >= 1.0.0", "Unity.XR.ARFoundation >= 1.0.0", "Unity.XR.ARFoundation.Editor >= 1.0.0", "Unity.XR.ARFoundation.InternalUtils >= 1.0.0", "Unity.XR.ARFoundation.VisualScripting >= 1.0.0", "Unity.XR.ARFoundation.VisualScripting.Editor >= 1.0.0", "Unity.XR.ARKit >= 1.0.0", "Unity.XR.ARKit.Editor >= 1.0.0", "Unity.XR.ARKit.FaceTracking >= 1.0.0", "Unity.XR.ARSubsystems >= 1.0.0", "Unity.XR.ARSubsystems.Editor >= 1.0.0", "Unity.XR.CoreUtils >= 1.0.0", "Unity.XR.CoreUtils.Editor >= 1.0.0", "Unity.XR.Management >= 1.0.0", "Unity.XR.Management.Editor >= 1.0.0", "Unity.XR.Simulation >= 1.0.0", "Unity.XR.Simulation.Editor >= 1.0.0", "UnityEditor.SpatialTracking >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEditor.XR.LegacyInputHelpers >= 1.0.0", "UnityEngine.SpatialTracking >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0", "UnityEngine.XR.LegacyInputHelpers >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity_Project\\SurAnim\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp-firstpass.csproj"}, "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Assembly-CSharp.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.InputSystem.ForUI.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Mathematics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Performance.Profile-Analyzer.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Rider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.Timeline.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.Shared.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VisualStudio.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.VSCode.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARAnalytics.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARCore.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.InternalUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARFoundation.VisualScripting.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARKit.FaceTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.ARSubsystems.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Management.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.csproj"}, "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\Unity.XR.Simulation.Editor.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.SpatialTracking.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.TestRunner.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.UI.csproj"}, "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Unity_Project\\SurAnim\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.202\\RuntimeIdentifierGraph.json"}}}}