%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 848409ee8ba454f42a5ddff137688a27, type: 3}
  m_Name: TestReferenceImageLibrary
  m_EditorClassIdentifier: 
  m_GuidLow: 4647545313610209675
  m_GuidHigh: 11388484120622119335
  m_DataStore:
    m_Storage: []
  m_Images:
  - m_SerializedGuid:
      m_GuidLow: 5635021770996272642
      m_GuidHigh: 9599431170484977801
    m_SerializedTextureGuid:
      m_GuidLow: 2625145118241824495
      m_GuidHigh: 17882567071252861320
    m_Size: {x: 0.1, y: 0.1}
    m_SpecifySize: 1
    m_Name: one
    m_Texture: {fileID: 0}
  - m_SerializedGuid:
      m_GuidLow: 5657834814528637882
      m_GuidHigh: 12247214586149009027
    m_SerializedTextureGuid:
      m_GuidLow: 294217663539233730
      m_GuidHigh: 18137028297261632793
    m_Size: {x: 0.1, y: 0.1}
    m_SpecifySize: 1
    m_Name: two
    m_Texture: {fileID: 0}
