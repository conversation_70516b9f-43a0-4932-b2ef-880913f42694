Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.43f1 (85497d293fa1) revision 8735101'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'en' Physical Memory: 65416 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.43f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Unity_Project/SurAnim
-logFile
Logs/AssetImportWorker1.log
-srvPort
53322
Successfully changed project path to: D:/Unity_Project/SurAnim
D:/Unity_Project/SurAnim
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19428]  Target information:

Player connection [19428]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 405381967 [EditorId] 405381967 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-AE5T6LH) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19428]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 405381967 [EditorId] 405381967 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-AE5T6LH) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19428] Host joined multi-casting on [***********:54997]...
Player connection [19428] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 10.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.43f1 (85497d293fa1)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity_Project/SurAnim/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3070 (ID=0x2484)
    Vendor:   NVIDIA
    VRAM:     8018 MB
    Driver:   32.0.15.7260
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56800
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.43f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.016494 seconds.
- Loaded All Assemblies, in  0.369 seconds
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 224 ms
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.533 seconds
Domain Reload Profiling: 898ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (156ms)
		LoadAssemblies (101ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (151ms)
				TypeCache.ScanAssembly (139ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (533ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (492ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (338ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (107ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.780 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 1662ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (21ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (524ms)
		LoadAssemblies (387ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (199ms)
				TypeCache.ScanAssembly (179ms)
			ScanForSourceGeneratedMonoScriptInfo (23ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (709ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (559ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 12.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4600 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (1.2 MB). Loaded Objects now: 5081.
Memory consumption went from 161.9 MB to 160.7 MB.
Total: 6.184600 ms (FindLiveObjects: 0.663200 ms CreateObjectMapping: 0.550000 ms MarkObjects: 4.265700 ms  DeleteObjects: 0.704400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 220836.204696 seconds.
  path: Assets/CODE/QRCodeContoller.cs
  artifactKey: Guid(a1fc68b2bfce56f418f9cbd82d945d77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CODE/QRCodeContoller.cs using Guid(a1fc68b2bfce56f418f9cbd82d945d77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'cd4a87c3e5bae8bd0bc5f4b1f6e04b8e') in 0.002925 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.498 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.096 seconds
Domain Reload Profiling: 1590ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (264ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1096ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (417ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 9.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4568 Unused Serialized files (Serialized files now loaded: 0)
Unloading 26 unused Assets / (1.3 MB). Loaded Objects now: 5085.
Memory consumption went from 155.7 MB to 154.4 MB.
Total: 4.617400 ms (FindLiveObjects: 0.355600 ms CreateObjectMapping: 0.385300 ms MarkObjects: 3.312800 ms  DeleteObjects: 0.563100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7 unused Assets / (1.1 MB). Loaded Objects now: 5087.
Memory consumption went from 85.1 MB to 83.9 MB.
Total: 7.558700 ms (FindLiveObjects: 0.432900 ms CreateObjectMapping: 0.400100 ms MarkObjects: 5.887500 ms  DeleteObjects: 0.837100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.477 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.117 seconds
Domain Reload Profiling: 1590ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (258ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1117ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (416ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 9.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4568 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.2 MB). Loaded Objects now: 5092.
Memory consumption went from 155.5 MB to 154.3 MB.
Total: 3.960300 ms (FindLiveObjects: 0.326100 ms CreateObjectMapping: 0.359600 ms MarkObjects: 2.753400 ms  DeleteObjects: 0.520600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 298ffd25b301d719d3458f47d11fdada -> e02b95c3f5f63f472a91d7ac99e710c1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.487 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.111 seconds
Domain Reload Profiling: 1595ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (264ms)
		LoadAssemblies (316ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1112ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (587ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 9.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4568 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.2 MB). Loaded Objects now: 5096.
Memory consumption went from 155.5 MB to 154.3 MB.
Total: 4.536600 ms (FindLiveObjects: 0.398300 ms CreateObjectMapping: 0.388600 ms MarkObjects: 3.210000 ms  DeleteObjects: 0.539000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 298ffd25b301d719d3458f47d11fdada -> e02b95c3f5f63f472a91d7ac99e710c1
========================================================================
Received Import Request.
  Time since last request: 8143.721305 seconds.
  path: Assets/CODE/Reports.cs
  artifactKey: Guid(2893b793bece2d8489f0d9afe9c66e5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CODE/Reports.cs using Guid(2893b793bece2d8489f0d9afe9c66e5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '6329255ec304b62ddf210e17b229ea86') in 0.002811 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.473 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.067 seconds
Domain Reload Profiling: 1536ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (254ms)
		LoadAssemblies (300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1067ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (546ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 9.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4567 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.2 MB). Loaded Objects now: 5100.
Memory consumption went from 155.2 MB to 154.0 MB.
Total: 3.982600 ms (FindLiveObjects: 0.322700 ms CreateObjectMapping: 0.278400 ms MarkObjects: 2.873900 ms  DeleteObjects: 0.507000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 782f49c966dcc532b057e35b15704485 -> e02b95c3f5f63f472a91d7ac99e710c1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.467 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.063 seconds
Domain Reload Profiling: 1527ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (252ms)
		LoadAssemblies (300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1063ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 9.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4568 Unused Serialized files (Serialized files now loaded: 0)
Unloading 24 unused Assets / (1.1 MB). Loaded Objects now: 5104.
Memory consumption went from 155.4 MB to 154.3 MB.
Total: 4.190100 ms (FindLiveObjects: 0.329700 ms CreateObjectMapping: 0.320700 ms MarkObjects: 2.998700 ms  DeleteObjects: 0.540400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/Reports.cs: a489fdb8bc61c2ad92d139253aac1192 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 782f49c966dcc532b057e35b15704485 -> e02b95c3f5f63f472a91d7ac99e710c1
